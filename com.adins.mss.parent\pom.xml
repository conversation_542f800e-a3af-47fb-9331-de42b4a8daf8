<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.adins.mss</groupId>
	<artifactId>com.adins.mss.parent</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<packaging>pom</packaging>
	<properties>
		<java.version>1.8</java.version>
		<adins-framework.version>1.0.22-SNAPSHOT</adins-framework.version>
		<adins-foundation.version>2.0.3b-SNAPSHOT</adins-foundation.version>
		<android.version>4.0.1.2</android.version>
		<aspectj.version>1.7.4</aspectj.version>
		<bcprov-15.version>1.45</bcprov-15.version>
		<cxf.version>3.1.14</cxf.version>
		<commons-lang3.version>3.2</commons-lang3.version>
		<commons-io.version>2.2</commons-io.version>
		<dwr.version>2.0.10</dwr.version>
		<ehcache.version>2.8.5</ehcache.version>
		<greendao.version>2.2.1</greendao.version>
		<gson.version>2.8.2</gson.version>
		<guava.version>20.0</guava.version>
		<hibernate-jpa.version>1.0.0.Final</hibernate-jpa.version>
		<hibernate-validator.version>5.4.2.Final</hibernate-validator.version>
		<hibernate3.version>4.2.21.Final</hibernate3.version>
		<hikaricp.version>2.4.13</hikaricp.version>
		<itext.version>2.1.7</itext.version>
		<jackson-fasterxml.version>2.7.7</jackson-fasterxml.version>
		<jackson-jaxrs.version>1.9.0</jackson-jaxrs.version>
		<javamelody.version>1.70.0</javamelody.version>
		<java-el.version>3.0.0</java-el.version>
		<jax-rs.version>2.0.1</jax-rs.version>
		<jexl.version>2.1.1</jexl.version>
		<jfreechart.version>1.0.19</jfreechart.version>
		<jms.version>2.0</jms.version>
		<joda-time.version>2.7</joda-time.version>
		<jtds.version>1.3.1</jtds.version>
		<jopt-simple.version>4.9</jopt-simple.version>
		<junit.version>4.13.2</junit.version>
		<mockito.version>4.11.0</mockito.version>
		<logback.version>1.2.3</logback.version>
		<opencsv.version>3.7</opencsv.version>
		<poi.version>3.14</poi.version>
		<poi-ooxml.version>3.14</poi-ooxml.version>
		<quartz.version>2.2.1</quartz.version>
		<servlet-api.version>3.1.0</servlet-api.version>
		<slf4j.version>1.7.25</slf4j.version>
		<spring-framework.version>3.2.18.RELEASE</spring-framework.version>
		<struts2.version>2.3.34</struts2.version>
		<spring-framework-oauth.version>2.0.14.RELEASE</spring-framework-oauth.version>
		<wildfly.version>8.2.1.Final</wildfly.version>
		<spring-security.version>3.2.10.RELEASE</spring-security.version>
	</properties>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.6</version>
				<executions>
					<execution>
						<id>prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/jacoco-reports</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>report-aggregate</id>
						<phase>verify</phase>
						<goals>
							<goal>report-aggregate</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/jacoco-aggregate</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
	<modules>
		<module>businesslogic-api</module>
		<module>core</module>
		<module>model</module>
		<module>webresources</module>
		<module>businesslogic-impl</module>
		<module>controller</module>
		<module>webapp</module>
		<module>base</module>
		<module>services</module>
		<module>webservices</module>
		<module>schedulerapp</module>
		<module>schedulerpackager</module>
	</modules>
  	<distributionManagement>
	    <snapshotRepository>
	      <id>snapshots</id>
	      <name>libs-snapshot</name>
	      <url>http://mss-webdev-svr:8081/artifactory/libs-snapshot-local</url>
	    </snapshotRepository>
  	</distributionManagement>
	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>${junit.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>${spring-framework.version}</version>
			<scope>test</scope>
		</dependency>	
		<dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
			<version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>	
	</dependencies>
</project>