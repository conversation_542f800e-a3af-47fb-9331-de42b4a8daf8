package com.adins.mss.model;

import static org.junit.Assert.assertNotNull;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;


@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath*:META-INF/spring/application-test-context.xml"
})

public class TblAgreementHistoryTest {
	
	@InjectMocks
    private TblAgreementHistory tblAgreementHistory;

	String uuidAgreementHistory = "uuid";
	String custNo = "custNo";
	String name = "name";
	String idNo = "idNo";
	String birthPlace = "place";
	String birthDt = "date";
	String emailAddress = "email";
	String phoneNumber = "phone";
	String type = "type";
	String taskIdPolo = "taskId";
	String orderNo = "orderNo";
	String appNo = "appNo";
	String source = "source";
	String dtmCrt = "dateCrt";
	String dtmUpd = "dtmUpd";
	
    @Before
	public void before() {
    	tblAgreementHistory = new TblAgreementHistory();
		MockitoAnnotations.openMocks(this);
	}

    @Test
	public void test_setterGetter() {
    	tblAgreementHistory.setUuidAgreementHistory(0);
    	tblAgreementHistory.setCustNo(custNo);
    	tblAgreementHistory.setName(name);
    	tblAgreementHistory.setIdNo(idNo);
    	tblAgreementHistory.setBirthPlace(birthPlace);
    	tblAgreementHistory.setBirthDt(null);
    	tblAgreementHistory.setEmailAddress(emailAddress);
    	tblAgreementHistory.setPhoneNumber(phoneNumber);
    	tblAgreementHistory.setType(type);
    	tblAgreementHistory.setTaskIdPolo(taskIdPolo);
    	tblAgreementHistory.setOrderNo(orderNo);
    	tblAgreementHistory.setAppNo(appNo);
    	tblAgreementHistory.setSource(source);
    	tblAgreementHistory.setDtmCrt(null);
    	tblAgreementHistory.setDtmUpd(null);
    	
    	tblAgreementHistory.getUuidAgreementHistory();
    	tblAgreementHistory.getCustNo();
    	tblAgreementHistory.getName();
    	tblAgreementHistory.getIdNo();
    	tblAgreementHistory.getBirthPlace();
    	tblAgreementHistory.getBirthDt();
    	tblAgreementHistory.getEmailAddress();
    	tblAgreementHistory.getPhoneNumber();
    	tblAgreementHistory.getType();
    	tblAgreementHistory.getTaskIdPolo();
    	tblAgreementHistory.getOrderNo();
    	tblAgreementHistory.getAppNo();
    	tblAgreementHistory.getSource();
    	tblAgreementHistory.getDtmCrt();
    	tblAgreementHistory.getDtmUpd();
		assertNotNull(tblAgreementHistory);
	}

    @Test
	public void test_initiation() {
    	tblAgreementHistory =  new TblAgreementHistory(1L);
    	tblAgreementHistory = new TblAgreementHistory(1L, custNo, name, idNo, birthPlace,
    			null, emailAddress, phoneNumber, type, taskIdPolo, orderNo,
    			appNo, source, null, null);
    }
}
