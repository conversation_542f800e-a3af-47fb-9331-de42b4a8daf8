package com.adins.mss.services.model.common;

import static org.junit.Assert.assertNotNull;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Unit tests for AddTaskPoloRequest class focusing on new custProtectCode field
 * added between revisions 6d55721 and d6e288d
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath*:META-INF/spring/application-test-context.xml"
})
public class AddTaskPoloRequestTest {

    @InjectMocks
    private AddTaskPoloRequest addTaskPoloRequest;

    String custProtectCode = "PROTECT001";

    @Before
    public void before() {
        addTaskPoloRequest = new AddTaskPoloRequest();
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void test_setterGetter() {
        // Test setting null value
        addTaskPoloRequest.setCustProtectCode(null);
        addTaskPoloRequest.getCustProtectCode();

        // Test setting a valid value
        addTaskPoloRequest.setCustProtectCode(custProtectCode);
        addTaskPoloRequest.getCustProtectCode();

        // Test setting empty string
        addTaskPoloRequest.setCustProtectCode("");
        addTaskPoloRequest.getCustProtectCode();

        assertNotNull(addTaskPoloRequest);
    }
}
