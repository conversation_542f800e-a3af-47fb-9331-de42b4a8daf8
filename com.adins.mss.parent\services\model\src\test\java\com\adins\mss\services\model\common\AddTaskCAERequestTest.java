package com.adins.mss.services.model.common;

import static org.junit.Assert.assertNotNull;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Unit tests for AddTaskCAERequest class focusing on new custProtectCode field
 * added between revisions 6d55721 and d6e288d
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath*:META-INF/spring/application-test-context.xml"
})
public class AddTaskCAERequestTest {

    @InjectMocks
    private AddTaskCAERequest addTaskCAERequest;

    String custProtectCode = "PROTECT001";

    @Before
    public void before() {
        addTaskCAERequest = new AddTaskCAERequest();
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void test_setterGetter() {
        // Test setting null value
        addTaskCAERequest.setCustProtectCode(null);
        addTaskCAERequest.getCustProtectCode();

        // Test setting a valid value
        addTaskCAERequest.setCustProtectCode(custProtectCode);
        addTaskCAERequest.getCustProtectCode();

        // Test setting empty string
        addTaskCAERequest.setCustProtectCode("");
        addTaskCAERequest.getCustProtectCode();

        assertNotNull(addTaskCAERequest);
    }
}
