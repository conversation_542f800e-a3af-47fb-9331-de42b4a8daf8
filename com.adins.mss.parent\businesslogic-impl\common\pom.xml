<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.adins.mss</groupId>
    <artifactId>com.adins.mss.businesslogic-impl</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <artifactId>com.adins.mss.businesslogic-impl.common</artifactId>
  <dependencies>
  	<dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>
  			com.adins.mss.businesslogic-api.common
  		</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>
  			com.adins.mss.businesslogic-api.am
  		</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>
  			com.adins.mss.businesslogic-api.collection
  		</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
  	 <dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>com.adins.mss.base.businesslogic</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>com.adins.framework.service.base</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>com.adins.framework.tool.password</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
	<dependency>
	    <groupId>org.apache.poi</groupId>
	    <artifactId>poi</artifactId>
	    <version>${poi.version}</version>
	</dependency>
  	<dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>com.adins.mss.services.model-public</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
  	<dependency>
		<groupId>com.adins.mss</groupId>
		<artifactId>com.adins.mss.services.model-newconfins</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</dependency>
  	<dependency>
  		<groupId>com.adins.foundation</groupId>
  		<artifactId>com.adins.foundation.workflow.bussinesslogic-impl.engine</artifactId>
  		<version>${adins-foundation.version}</version>
  		<scope>test</scope>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>com.adins.framework.tool.geolocation</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
  	<dependency>
		<groupId>org.apache.commons</groupId>
		<artifactId>commons-jexl</artifactId>
		<version>${jexl.version}</version>
	</dependency>
	<dependency>
		<groupId>com.adins.mss.mobile</groupId>
		<artifactId>com.adins.mss.mobile.base</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</dependency>
		<dependency>
		<groupId>org.greenrobot</groupId>
		<artifactId>greendao</artifactId>
		<version>${greendao.version}</version>
	</dependency>
	<dependency>
		<groupId>com.google.android</groupId>
		<artifactId>android</artifactId>
		<version>${android.version}</version>
	</dependency>
	<dependency>
		<groupId>com.adins.mss</groupId>
		<artifactId>com.adins.mss.services.model-newconfins</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</dependency>
	<dependency>
		<groupId>org.jfree</groupId>
		<artifactId>jfreechart</artifactId>
		<version>${jfreechart.version}</version>
	</dependency>
	<dependency>
  		<groupId>joda-time</groupId>
  		<artifactId>joda-time</artifactId>
  		<version>${joda-time.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>com.adins.framework.tool.ldap</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
  	<dependency>
		<groupId>com.adins.mss</groupId>
		<artifactId>com.adins.mss.businesslogic-api.survey</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</dependency>
	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>com.adins.framework.persistence.dao-hibernate</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
  	<dependency>
		<groupId>org.springframework.security.oauth</groupId>
		<artifactId>spring-security-oauth2</artifactId>
		<version>${spring-framework-oauth.version}</version>
	</dependency>
	<dependency>
	     <groupId>com.opencsv</groupId>
	     <artifactId>opencsv</artifactId>
	     <version>${opencsv.version}</version>
  	</dependency>
  	<dependency>
	    <groupId>com.google.maps</groupId>
	    <artifactId>google-maps-services</artifactId>
	    <version>0.1.17</version>
	</dependency>
  	<dependency>
		<groupId>javax.servlet</groupId>
		<artifactId>javax.servlet-api</artifactId>
  		<version>${servlet-api.version}</version>
  		<scope>provided</scope>
	</dependency>	
	<dependency>
		<groupId>net.sf.ehcache</groupId>
		<artifactId>ehcache</artifactId>
		<version>${ehcache.version}</version>
	</dependency>
	<dependency>
	     <groupId>com.itextpdf</groupId>
	     <artifactId>itextpdf</artifactId>
	     <version>5.0.6</version>
	 </dependency>
	 <dependency>
	     <groupId>com.itextpdf</groupId>
	     <artifactId>io</artifactId>
	     <version>7.0.0</version>
	 </dependency> 
	 <dependency>
	     <groupId>com.itextpdf</groupId>
	     <artifactId>layout</artifactId>
	     <version>7.0.0</version>
	 </dependency>
	 <!-- Test dependencies -->
	 <dependency>
		<groupId>junit</groupId>
		<artifactId>junit</artifactId>
		<version>${junit.version}</version>
		<scope>test</scope>
	</dependency>
	<dependency>
		<groupId>org.springframework</groupId>
		<artifactId>spring-test</artifactId>
		<version>${spring-framework.version}</version>
		<scope>test</scope>
	</dependency>
	<dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
		<version>${mockito.version}</version>
        <scope>test</scope>
    </dependency>
  </dependencies>
</project>