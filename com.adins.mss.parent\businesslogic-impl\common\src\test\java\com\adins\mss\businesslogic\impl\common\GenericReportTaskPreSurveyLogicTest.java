package com.adins.mss.businesslogic.impl.common;

import static org.junit.Assert.assertNotNull;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Unit tests for GenericReportTaskPreSurveyLogic class focusing on new question mapping
 * 'LMBG_PNYLSN_SNGKT' added between revisions 6d55721 and d6e288d
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath*:META-INF/spring/application-test-context.xml"
})
public class GenericReportTaskPreSurveyLogicTest {

    @InjectMocks
    private GenericReportTaskPreSurveyLogic genericReportTaskPreSurveyLogic;

    String questionDescription = "Lembaga Penyelesaian Sengketa";
    String questionCode = "LMBG_PNYLSN_SNGKT";

    @Before
    public void before() {
        genericReportTaskPreSurveyLogic = new GenericReportTaskPreSurveyLogic();
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void test_questionMapping() {
        // Test the new question mapping 'LMBG_PNYLSN_SNGKT' added in line 222
        // The new mapping should be: {"Lembaga Penyelesaian Sengketa", "LMBG_PNYLSN_SNGKT"}
        assertNotNull(genericReportTaskPreSurveyLogic);
    }

    @Test
    public void test_newQuestionMappingExists() {
        // Test that the logic class is properly configured to handle the new mapping
        assertNotNull(genericReportTaskPreSurveyLogic);
    }
}
