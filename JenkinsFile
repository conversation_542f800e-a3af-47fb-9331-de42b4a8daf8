pipeline {
    agent any
	environment {
        SONAR_PROJECT_KEY = 'WOMFMSS_20250616_WEB'
		SONAR_PROJECT_NAME = '20250616_WOMFMSS_OPSI_PENYELESAIAN_SENGKETA'
		STAGING_PARENT = false
    }
	parameters {
		choice(name: 'TASK', choices: ['COMMIT_SCAN', 'BUILD', 'BUILD_ONLY'], description: 'Custom TASK to control pipeline steps')
    }
    stages {
        stage('Prepare Workspace') {
            steps {
                checkout scm
				bat 'git reset --hard HEAD'
				bat 'git clean -fdx'
            }
        }
		stage('Conditional Step for TASK BUILD_ONLY') {
            when {
                expression {
                    return params.TASK == 'BUILD_ONLY'
                }
            }
			tools {
				jdk 'JDK 8' // Ensure this JDK version is configured in Jenkins
			}
            steps {
                echo 'Running special steps because TASK is set to "BUILD_ONLY"'
                script {
                    dir('com.adins.mss.parent') {
                        bat "mvn -s ${env.MAVEN_SETTINGS} clean package -e -DskipTests"
                    }
					if (STAGING_PARENT.toString().trim() == "true") {
						dir('com.adins.mss-staging.parent') {
							bat "mvn -s ${env.MAVEN_SETTINGS} clean package -e -DskipTests"
						}
					}
                }
            }
        }
		stage('Compile Maven') {
			when {
                expression {
                    return params.TASK != 'BUILD_ONLY'
                }
            }
		    tools {
				jdk 'JDK 8' // Ensure this JDK version is configured in Jenkins
			}
            steps {
                script {
                    dir('com.adins.mss.parent') {
                        bat "mvn -s ${env.MAVEN_SETTINGS} clean package -e"
                    }
					if (STAGING_PARENT.toString().trim() == "true") {
						dir('com.adins.mss-staging.parent') {
							bat "mvn -s ${env.MAVEN_SETTINGS} clean package -e"
						}
					}
                }
            }
        }
        stage('SonarQube Analysis') {
			when {
                expression {
                    return params.TASK != 'BUILD_ONLY'
                }
            }
            tools {
                jdk "JDK 11" // the name you have given the JDK installation in Global Tool Configuration
            }
            environment {
                scannerHome = tool 'SonarQube' // the name you have given the Sonar Scanner (in Global Tool Configuration)
            }
            steps {
				script {
					def sonarSources = "com.adins.mss.parent"
					if (STAGING_PARENT.toString().trim() == "true") {
						sonarSources = sonarSources + ",com.adins.mss-staging.parent"
					}
					
					echo "sonarSources = ${sonarSources}"
					
					withSonarQubeEnv('SonarQube') {
						withCredentials([string(credentialsId: 'SONAR_TOKEN', variable: 'SECRET')]) {
							bat """
								sonar-scanner \
								-Dsonar.projectName=${SONAR_PROJECT_NAME} \
								-Dsonar.projectKey=${SONAR_PROJECT_KEY} \
								-Dsonar.sources=${sonarSources} \
								-Dsonar.language=java \
								-Dsonar.java.binaries=**/target/classes \
								-Dsonar.inclusions=**/*.java \
								-Dsonar.exclusions=**/com/adins/mss/businesslogic/impl/interfacing/IntNoneFormLogic.java,**/com/adins/mss/businesslogic/impl/interfacing/IntNcR3FormLogic.java,**/com/adins/mss/businesslogic/impl/interfacing/IntStagingFormLogic.java,**/com/adins/mss/businesslogic/impl/interfacing/IntFTPFormLogic.java \
								-Dsonar.coverage.exclusions=**/src/test/java/** \
								-Dsonar.junit.reportPaths=**/target/surefire-reports \
								-Dsonar.jacoco.reportPaths=**/target/jacoco.exec \
								-Dsonar.coverage.jacoco.xmlReportPaths=**/target/jacoco-reports/jacoco.xml
							"""							
						}
					}
				}
            }
        }
		stage('Quality Gate') {
			when {
                expression {
                    return params.TASK != 'BUILD_ONLY'
                }
            }
			steps {
                timeout(time: 1, unit: 'HOURS') {
                    script {
                        def qg = waitForQualityGate()
                        if (qg.status != 'OK') {
                            currentBuild.result = 'UNSTABLE'
                            echo 'Quality gate failed'
                        }
                    }
                }
            }
        }
        stage('Archive WAR Files') {
            when {
					expression {
						return currentBuild.result != 'UNSTABLE' && params.TASK != 'COMMIT_SCAN'
					}
				}
           steps {
                // Archive all .war files
                script{
					def timestamp = new Date().format("yyyyMMddHHmmss")
                    def infoTimestamp = "#${BUILD_NUMBER}-${timestamp}"
                    def revision = bat(script: "git rev-parse --short HEAD", returnStdout: true).trim().split('\n')[-1]
                    def warFiles = findFiles(glob: '**/*.war')
                    warFiles.each { file ->
                        def directory = file.path.substring(0, file.path.lastIndexOf("\\"))
                        def newFileName = "${directory}/${file.name.replace('.war', '')}-${revision}-${infoTimestamp}.war"
                        bat "move ${file.path} ${newFileName}"
                        archiveArtifacts artifacts: newFileName, fingerprint: true, onlyIfSuccessful: true
                        echo "Archived WAR file: ${newFileName}"
                    }
					
					if (params.SQL_SCRIPT_TO_ARCHIVE != null && params.SQL_SCRIPT_TO_ARCHIVE != "") {
						def scriptFolder = params.SQL_SCRIPT_TO_ARCHIVE.split(',')
						echo "scriptFolder = ${scriptFolder}"
						scriptFolder.each { folder ->
							def directory = "${env.WORKSPACE}\\"+folder.substring(0, folder.lastIndexOf("\\"))
							echo "directory folder = ${directory}"
							def folderName = folder.split('\\\\').last()
							echo "folderName = ${folderName}"
							def archiveName = "${env.WORKSPACE}\\${folderName}-${revision}-${infoTimestamp}.zip"
							echo "archiveName folder = ${archiveName}"
							powershell "Compress-Archive -Path ${env.WORKSPACE}\\${folder} -DestinationPath ${archiveName} -Force"
							archiveArtifacts artifacts: "${folderName}-${revision}-${infoTimestamp}.zip", fingerprint: true, onlyIfSuccessful: true
						}
					}
                }
            }
        }
    }
	
	post {
        always {
            script {
				//echo all parameter
				echo "Parameter TASK = ${params.TASK}"
				
                // Rule 1: Keep 5 successful builds with parameter TASK=BUILD_ONLY
                def rule1Count = 0
                def buildsToDelete = []
                currentBuild.rawBuild.project.builds.each { build ->
                    if (build.result?.toString() == 'SUCCESS' &&
                        (build.getAction(ParametersAction)?.getParameter('TASK')?.value != 'COMMIT_SCAN')) {
                        if (rule1Count < 5) {
                            rule1Count++
                        } else {
                            buildsToDelete << build
                        }
                    }
                }

                // Rule 2: Keep only 10 builds total
                def rule2Count = 0
                currentBuild.rawBuild.project.builds.each { build ->
                    if (!buildsToDelete.contains(build)) {
                        if (rule2Count < 50) {
                            rule2Count++
                        } else {
                            buildsToDelete << build
                        }
                    }
                }

                // Rule 3: Delete all other builds
                buildsToDelete.each { build ->
                    build.delete()
                }
            }
        }
    }
}